<?php declare(strict_types=1);

namespace App\PostType\Workshop\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property WorkshopLocalization[]|OneHasMany $localizations {1:M WorkshopLocalization::$workshop}
 * @property WorkshopApplicant[]|OneHasMany $applicants {1:M WorkshopApplicant::$workshop}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property-read int|null $length {virtual}
 * @property-read string|null $place {virtual}
 */
class Workshop extends BaseEntity implements ParentEntity, HasImages
{
	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	public function getLocalization(Mutation $mutation): LocalizationEntity
	{
		$localization = $this->getLocalizations()->getBy(['mutation' => $mutation]);
		assert($localization instanceof WorkshopLocalization);
		return $localization;
	}

	protected function getterLength(): ?int
	{
		return $this->cf->base->length ? (int)$this->cf->base->length : null;
	}

	protected function getterPlace(): ?string
	{
		return $this->cf->base->place ?? null;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->cf->base->mainImage) ? $this->cf->base->mainImage->getEntity() : null;
	}
}
