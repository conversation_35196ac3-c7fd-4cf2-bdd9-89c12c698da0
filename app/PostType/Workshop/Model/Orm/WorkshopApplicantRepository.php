<?php declare(strict_types=1);

namespace App\PostType\Workshop\Model\Orm;

use App\Model\Orm\BackedEnumWrapper;
use App\Model\Orm\BaseEntity;
use App\PostType\Workshop\Model\Enum\ApplicantExperience;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Repository\Repository;


class WorkshopApplicantRepository extends Repository
{
	public static function getEntityClassNames(): array
	{
		return [WorkshopApplicant::class];
	}
}
