<?php declare(strict_types = 1);

namespace App\PostType\Workshop\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Author\Model\Orm\Author;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\BlogTag\Model\BlogTagModel;
use App\PostType\Workshop\FrontModule\Components\RegistrationForm\RegistrationForm;
use App\PostType\Workshop\FrontModule\Components\RegistrationForm\RegistrationFormControl;
use App\PostType\Workshop\FrontModule\Components\RegistrationForm\RegistrationFormFactory;
use App\PostType\Workshop\FrontModule\Components\WorkshopDates\WorkshopDates;
use App\PostType\Workshop\FrontModule\Components\WorkshopDates\WorkshopDatesFactory;
use App\PostType\Workshop\FrontModule\Components\WorkshopLectors\WorkshopLectors;
use App\PostType\Workshop\FrontModule\Components\WorkshopLectors\WorkshopLectorsFactory;
use App\PostType\Workshop\FrontModule\Components\WorkshopProgram\WorkshopProgram;
use App\PostType\Workshop\FrontModule\Components\WorkshopProgram\WorkshopProgramFactory;
use App\PostType\Workshop\FrontModule\Components\WorkshopTopics\WorkshopTopics;
use App\PostType\Workshop\FrontModule\Components\WorkshopTopics\WorkshopTopicsFactory;
use App\PostType\Workshop\Model\Orm\Workshop;
use App\PostType\Workshop\Model\Orm\WorkshopLocalization;
use Nette\Application\UI\Form;

/**
 * @method WorkshopLocalization getObject()
 */
final class WorkshopPresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	public function __construct(
		private readonly WorkshopDatesFactory $workshopDatesFactory,
		private readonly WorkshopLectorsFactory $workshopLectorsFactory,
		private readonly WorkshopProgramFactory $workshopProgramFactory,
		private readonly WorkshopTopicsFactory $workshopTopicsFactory,
		private readonly RegistrationFormControl $registrationFormFactory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(WorkshopLocalization $object): void
	{
		$this->setObject($object);
	}

	public function renderDefault(WorkshopLocalization $object): void
	{
		$this->template->workshop = $object;
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

	public function createComponentWorkshopDates(): WorkshopDates
	{
		return $this->workshopDatesFactory->create($this->getObject());
	}

	public function createComponentWorkshopLectors(): WorkshopLectors
	{
		return $this->workshopLectorsFactory->create($this->getObject());
	}

	public function createComponentWorkshopProgram(): WorkshopProgram
	{
		return $this->workshopProgramFactory->create($this->getObject());
	}

	public function createComponentWorkshopTopics(): WorkshopTopics
	{
		return $this->workshopTopicsFactory->create($this->getObject());
	}

	public function createComponentRegistrationForm(): RegistrationForm
	{
		return $this->registrationFormFactory->create($this->getObject());
	}
}
