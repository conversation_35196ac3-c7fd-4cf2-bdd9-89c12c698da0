<?php declare(strict_types=1);

namespace App\PostType\Workshop\FrontModule\Components\RegistrationForm;

use App\FrontModule\Components\HasAntispamInput;
use App\Model\TranslatorDB;
use App\PostType\Workshop\Model\Enum\ApplicantExperience;
use App\PostType\Workshop\Model\Orm\WorkshopApplicant;
use App\PostType\Workshop\Model\Orm\WorkshopLocalization;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
class RegistrationFormControl extends Control
{
	use HasAntispamInput;

	public function __construct(
		private readonly WorkshopLocalization $workshop,
		private readonly TranslatorDB $translator,
		private readonly RegistrationForm $registrationForm,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/registrationForm.latte');
	}

	public function create(): Form
	{
		$form = $this->registrationForm->create($this->workshop);
		assert($form instanceof Form);

		$this->attachAntispamTo($form);

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		try {
			$applicant = new WorkshopApplicant();
			$applicant->name = $values->name;
			$applicant->bakery = $values->bakery;
			$applicant->email = $values->email;
			$applicant->phone = $values->phone;
			$applicant->experience = ApplicantExperience::from($values->experience);
			$applicant->message = $values->message;
			$applicant->workshop = $this->workshop->workshop;

			$this->orm->workshopApplicant->persistAndFlush($applicant);

			$this->presenter->flashMessage('form_success_registration', 'success');
			$this->presenter->redirect('this');

		} catch (Throwable $e) {
			$form->addError('form_error_registration');
		}
	}

	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

}
