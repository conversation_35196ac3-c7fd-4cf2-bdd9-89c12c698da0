{php $control['form']->action .= "#frm-registrationForm-form"}

{snippet form}
	{form form class: 'f-community u-mb-lg block-loader', data-naja: '', novalidate: "novalidate"}
		{* <h2>
			{_newsletter_title}
		</h2> *}

		{control messageForForm, $flashes, $form, TRUE, ['timeToggle'=>true]}

		{include '../inp.latte', form: $form, name: name, labelLang: 'form_label_name', placeholderLang=>'form_label_name', validate: true}
		{include '../inp.latte', form: $form, name: bakery, labelLang: 'form_label_bakery', placeholderLang=>'form_label_bakery', validate: false}
		{include '../inp.latte', form: $form, name: email, labelLang: 'form_label_email', placeholderLang=>'form_label_email', validate: true}
		{include '../inp.latte', form: $form, name: phone, labelLang: 'form_label_phone', placeholderLang=>'form_label_phone', validate: true}
		{include '../inp.latte', form: $form, name: experience, labelLang: 'form_label_experience', placeholderLang=>'form_label_experience', validate: true}
		{include '../inp.latte', form: $form, name: dates, labelLang: 'form_label_dates', placeholderLang=>'form_label_dates', validate: true}
		{include '../inp.latte', form: $form, name: message, labelLang: 'form_label_message', placeholderLang=>'form_label_message', validate: false}

		<p>
			<button type="submit" class="btn">
				<span class="btn__text">
					{_btn_send}
				</span>
			</button>
		</p>

		{*ANTISPAM*}
{*		{if isset($form['antispamNoJs'])}*}
{*			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">*}
{*				<label n:name="antispamNoJs">*}
{*					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}*}
{*				</label>*}
{*				<span class="inp-fix">*}
{*					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">*}
{*				</span>*}
{*			</p>*}
{*		{/if}*}
		{*/ANTISPAM*}

	{/form}
{/snippet}
