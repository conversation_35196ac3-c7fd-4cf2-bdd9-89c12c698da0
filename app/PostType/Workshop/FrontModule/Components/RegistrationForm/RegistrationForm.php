<?php declare(strict_types=1);

namespace App\PostType\Workshop\FrontModule\Components\RegistrationForm;

use App\Model\Form\CommonFormFactory;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use App\PostType\Workshop\Model\Enum\ApplicantExperience;
use App\PostType\Workshop\Model\Orm\WorkshopApplicant;
use App\PostType\Workshop\Model\Orm\WorkshopLocalization;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
class RegistrationForm
{
	public function __construct(
		private readonly WorkshopLocalization $workshop,
		private readonly CommonFormFactory $formFactory,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
	)
	{
	}

	protected function create(): Form
	{
		$form = $this->formFactory->create();
		$form->setTranslator($this->translator);

		$form->addText('name', 'form_label_name')
			->setRequired('form_required_name')
			->setMaxLength(255);

		$form->addText('bakery', 'form_label_bakery')
			->setMaxLength(255);

		$form->addEmail('email', 'form_label_email')
			->setRequired('form_required_email')
			->setMaxLength(255);

		$form->addText('phone', 'form_label_phone')
			->setRequired('form_required_phone')
			->setMaxLength(255);

		$experienceOptions = [
			ApplicantExperience::Beginner->value => 'experience_beginner',
			ApplicantExperience::Intermediate->value => 'experience_intermediate',
			ApplicantExperience::Advanced->value => 'experience_advanced',
		];
		$form->addSelect('experience', 'form_label_experience', $experienceOptions)
			->setRequired('form_required_experience');

		$datesOptions = [];
		foreach ($this->workshop->dates as $date) {
			if($date->isPreparation()){
				$datesOptions[sprintf("%s/%s", $date->from->format('Y-m-d H:i'), $date->to->format('Y-m-d H:i'))] = sprintf("%s - %s", $date->from->format('d. m. Y H:i'), $date->to->format('d. m. Y H:i'));
			}
		}
		$form->addSelect('dates', 'form_label_dates', $datesOptions)
			->setRequired('form_required_dates');

		$form->addTextArea('message', 'form_label_message')
			->setRequired('form_required_message')
			->setMaxLength(2000);

		$form->addSubmit('send', 'form_submit_registration');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		try {
			$applicant = new WorkshopApplicant();
			$applicant->name = $values->name;
			$applicant->bakery = $values->bakery;
			$applicant->email = $values->email;
			$applicant->phone = $values->phone;
			$applicant->experience = ApplicantExperience::from($values->experience);
			$applicant->message = $values->message;
			$applicant->workshop = $this->workshop->workshop;

			$this->orm->workshopApplicant->persistAndFlush($applicant);

			$this->presenter->flashMessage('form_success_registration', 'success');
			$this->presenter->redirect('this');

		} catch (Throwable $e) {
			$form->addError('form_error_registration');
		}
	}

	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}
}
