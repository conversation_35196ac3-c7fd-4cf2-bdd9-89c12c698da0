services:
	- App\AdminModule\Presenters\AdminAccessChecker(
		%admin.allowedIpRanges%
	)

	- App\Model\AdminEmails(
		adminEmailDomains: %emailDomains.admin%
		developerEmailDomains: %emailDomains.developer%
	)

parameters:
	admin:
		allowedIpRanges: []

	emailDomains:
		admin: []
		developer: []

	config:
		adminMenu:
			Modules:
				- {title: Library, resource: Admin:Library, action: :Admin:Library:default, icon: images}
				- {title: Pages, resource: Page:Admin:Page, action: :Page:Admin:Page:default, icon: file}
				# - {title: Products, resource: Admin:Catalog, action: :Admin:Catalog:default, icon: store}
				# - {title: Seolink, resource: SeoLink:Admin:SeoLink, action: :SeoLink:Admin:SeoLink:default, icon: link}
				- {title: Blog, resource: Blog:Admin:Blog, action: :Blog:Admin:Blog:default, icon: book-open}
				# - {title: BlogTag, resource: BlogTag:Admin:BlogTag, action: :BlogTag:Admin:BlogTag:default, icon: tag, sub: true}
				- {title: Author, resource: Author:Admin:Author, action: :Author:Admin:Author:default, icon: user-alt, sub: true}
				- {title: Workshop, resource: Workshop:Admin:Workshop, action: :Workshop:Admin:Workshop:default, icon: cogs}
				- {title: Uchazeči, resource: Workshop:Admin:Workshop, action: :Workshop:Admin:Workshop:applicants, icon: user, sub: true}
				# - {title: Holidays, resource: Admin:Holiday, action: :Admin:Holiday:default, icon: map-signs}
				# - {title: Orders, resource: Admin:Order, action: :Admin:Order:default, icon: list}

			Lists: # veci které se plní frontenedem
				- {title: E-mails, resource: Admin:Newsletter, action: :Admin:Newsletter:default, icon: envelope}

			Settings:
				- {title: MenuMain, resource: MenuMain:Admin:MenuMain, action: :MenuMain:Admin:MenuMain:default, icon: file}
				# - {title: Transports, resource: Admin:DeliveryMethod, action: :Admin:DeliveryMethod:default, icon: file}
				# - {title: Payments, resource: Admin:PaymentMethod, action: :Admin:PaymentMethod:default, icon: file}
				# - {title: Vouchers, resource: Admin:Voucher, action: :Admin:Voucher:default, icon: file}
				- {title: System strings, resource: Admin:String, action: :Admin:String:default, icon: file}
				- {title: Mutations, resource: Admin:Mutation, action: :Admin:Mutation:default, icon: file}
				# - {title: States, resource: Admin:State, action: :Admin:State:default, icon: file}
				- {title: Files, resource: Admin:FileLibrary, action: :Admin:FileLibrary:default, icon: file}
				# - {title: Parameters, resource: Admin:Parameter, action: :Admin:Parameter:default, icon: file}
				- {title: Users, resource: Admin:User, action: :Admin:User:default, icon: file}
				- {title: E-mails template, resource: Admin:Email, action: :Admin:Email:default, icon: file}
				- {title: Redirects, resource: Admin:Redirect, action: :Admin:Redirect:default, icon: file}
			-
#				- {title: Šablony, resource: Admin:Template, action: :Admin:Template:default, icon: file}
				# - {title: Elastic search, resource: Admin:Elastic, action: :Admin:Elastic:default, icon: file, devOnly: true} #vidi jen developeri
				- {title: Cache, resource: Admin:Cache, action: :Admin:Cache:default, icon: file, devOnly: true} #vidi jen developeri
				# - {title: API tokens, resource: Admin:ApiToken, action: :Admin:ApiToken:default, icon: file, devOnly: true}
				- {title: Help, resource: superadmin, action: :Admin:Help:default, icon: file}
				- {title: About, resource: superadmin, action: :Admin:Help:about, icon: file}

			# -
				# - {title: Styleguide, resource: Admin:Styleguide, action: :Admin:Styleguide:default, icon: file, devOnly: true}


		modules:
			Admin:Homepage: Homepage
			Page:Admin:Page: Page
			Blog:Admin:Blog: Blog
			BlogTag:Admin:BlogTag: BlogTag
			Author:Admin:Author: Author
			Admin:Library: Library
			Admin:File: File
			Admin:FileLibrary: File
			Admin:Catalog: Catalog
			Admin:Product: Products
			Admin:User: Users
			Admin:Parameter: Parameters
			Admin:Email: E-mails
			Admin:String: System strings
			Admin:Developer: Developerské nastavení
			Admin:Help: Help
			Admin:Newsletter: Newsletter
			Admin:Place: Place
			Admin:Search: Search
			Admin:Search2: Search
			Admin:Template: Templates
			Admin:Redirect: Redirect
			Admin:Mutation: Mutation
			Admin:Elastic: Elastic
			Admin:State: State
			Admin:Styleguide: Styleguide
			SeoLink:Admin:SeoLink: Seolink
			Admin:Cache: Cache
			Admin:ApiToken: API tokeny
			Admin:Holiday: Holiday
			Admin:Order: Order
			Admin:DeliveryMethod: Transport
			Admin:PaymentMethod: Payment
			Admin:Voucher: Voucher
			MenuMain:Admin:MenuMain: MenuMain
			Workshop:Admin:Workshop: Workshop
			Workshop:Admin:WorkshopApplicant: WorkshopApplicant


		adminPaging: 20
		adminImagePaging: 32
		productPaging: 20

		tabs:
			pages: [
				images,
				videos,
				files,
				linkedCategories,
				seo,
				settings,
			]
			products: [
				variants,
				content,
				images,
				files,
				params,
				links,
				videos,
				reviews,
				articlePages,
				accessories, # doporucujeme
				presents,
				similar, #podobne produkty
				product, #podobne produkty pro vyprodany produkt
				contents,
				seo,
				settings,
			]
			emailTemplates: [
				files,
			]

		tabsHideOnlyOnTemplates:
			pages:
#				files:
#				links:
#				videos:
				attproducts:
#					- Article:detail
#					- Article:default
				pages:
#					- Article:detail
#					- Article:default
				faqs:
#					- Article:detail
#					- Article:default
				reviews:
#					- Article:detail
#					- Article:default
				attproductsReview:
#					- Article:detail
#					- Article:default



		tabsShowOnlyOnTemplates: # note: tabs are defined in admin.neon
			pages:
				params:
#					- Article:detail
				linkedCategories:
					- Catalog:default
#				faqs:
#				reviews:

includes:
	- lang/lang.neon
