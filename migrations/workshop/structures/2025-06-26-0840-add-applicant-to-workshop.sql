CREATE TABLE `workshop_applicant`
(
	`id`         int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`name`       varchar(255)  NOT NULL,
	`bakery`     varchar(255) NULL,
	`email`      varchar(255)  NOT NULL,
	`phone`      varchar(64)   NOT NULL,
	`message`    varchar(2048) NOT NULL,
	`experience` varchar(64)   NOT NULL,
	`agreedAt`   datetime      NOT NULL,
	`workshop`   int(11) NOT NULL,
	FOREIGN KEY (`workshop`) REFERENCES `workshop` (`id`) ON DELETE CASCADE
) ENGINE='InnoDB' COLLATE 'utf8mb4_general_ci';
